var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:"attr-list"},[_c('CommonAttr'),_c('el-form',[_c('el-form-item',{attrs:{"label":"内容"}},[_c('el-input',{attrs:{"type":"textarea","rows":3},model:{value:(_vm.curComponent.propValue),callback:function ($$v) {_vm.$set(_vm.curComponent, "propValue", $$v)},expression:"curComponent.propValue"}})],1)],1)],1)
}
var staticRenderFns = []

export { render, staticRenderFns }