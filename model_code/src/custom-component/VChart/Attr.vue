var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:"attr"},[_c('CommonAttr'),_c('el-form',[_c('el-form-item',{attrs:{"label":"标题"}},[_c('el-switch',{attrs:{"active-text":"显示标题"},model:{value:(_vm.option.title.show),callback:function ($$v) {_vm.$set(_vm.option.title, "show", $$v)},expression:"option.title.show"}}),_c('el-input',{attrs:{"placeholder":"请输入内容"},model:{value:(_vm.option.title.text),callback:function ($$v) {_vm.$set(_vm.option.title, "text", $$v)},expression:"option.title.text"}})],1),_c('el-form-item',{attrs:{"label":"工具提示"}},[_c('el-switch',{attrs:{"active-text":"显示提示"},model:{value:(_vm.option.tooltip.show),callback:function ($$v) {_vm.$set(_vm.option.tooltip, "show", $$v)},expression:"option.tooltip.show"}})],1),_c('el-form-item',{attrs:{"label":"图例"}},[_c('el-switch',{attrs:{"active-text":"显示图例"},model:{value:(_vm.option.legend.show),callback:function ($$v) {_vm.$set(_vm.option.legend, "show", $$v)},expression:"option.legend.show"}})],1),_c('el-form-item',{attrs:{"label":"横坐标"}},[_c('el-switch',{attrs:{"active-text":"显示横坐标"},model:{value:(_vm.option.xAxis.show),callback:function ($$v) {_vm.$set(_vm.option.xAxis, "show", $$v)},expression:"option.xAxis.show"}})],1),_c('el-form-item',[_c('el-dropdown',[_c('span',{staticClass:"el-dropdown-link"},[_vm._v(" 更换图表类型"),_c('i',{staticClass:"el-icon-arrow-down el-icon--right"})]),_c('el-dropdown-menu',{attrs:{"slot":"dropdown"},slot:"dropdown"},_vm._l((_vm.charts),function(chart,index){return _c('span',{key:index,on:{"click":function($event){return _vm.selectchart(chart.title)}}},[_c('el-dropdown-item',[_vm._v(_vm._s(chart.name))])],1)}),0)],1)],1),_c('el-form-item',{attrs:{"label":"静态数据源"}},[_c('el-button',{on:{"click":_vm.openStaticWinbox}},[_vm._v("修改数据")])],1)],1),_c('el-dialog',{attrs:{"title":"数据修改","visible":_vm.dialogVisible,"width":"75%"},on:{"update:visible":function($event){_vm.dialogVisible=$event}}},[_c('div',{ref:"ace",staticClass:"ace"}),_c('span',{staticClass:"dialog-footer",attrs:{"slot":"footer"},slot:"footer"},[_c('el-button',{attrs:{"type":"primary"},on:{"click":_vm.updatedata}},[_vm._v("更新数据")])],1)])],1)
}
var staticRenderFns = []

export { render, staticRenderFns }