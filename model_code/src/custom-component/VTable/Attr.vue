var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:"attr-list v-talbe-attr"},[_c('CommonAttr'),_c('EditTable'),_c('el-form',[_c('el-form-item',{attrs:{"label":"斑马纹"}},[_c('el-switch',{model:{value:(_vm.propValue.stripe),callback:function ($$v) {_vm.$set(_vm.propValue, "stripe", $$v)},expression:"propValue.stripe"}})],1),_c('el-form-item',{attrs:{"label":"表头加粗"}},[_c('el-switch',{model:{value:(_vm.propValue.thBold),callback:function ($$v) {_vm.$set(_vm.propValue, "thBold", $$v)},expression:"propValue.thBold"}})],1)],1)],1)
}
var staticRenderFns = []

export { render, staticRenderFns }