var render = function render(){var _vm=this,_c=_vm._self._c;return _c('table',{staticClass:"v-table"},[_c('tbody',_vm._l((_vm.propValue.data),function(item,index){return _c('tr',{key:index,class:{
        stripe: _vm.propValue.stripe && index % 2,
        bold: _vm.propValue.thBold && index === 0,
      }},_vm._l((item),function(e,i){return _c('td',{key:i},[_vm._v(_vm._s(e))])}),0)}),0)])
}
var staticRenderFns = []

export { render, staticRenderFns }