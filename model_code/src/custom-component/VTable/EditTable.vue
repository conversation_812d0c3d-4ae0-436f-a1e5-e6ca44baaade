var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:"edit-table"},[_c('table',{on:{"dblclick":_vm.onDblclick}},[_c('tbody',_vm._l((_vm.tableData),function(item,row){return _c('tr',{key:row},_vm._l((item),function(e,col){return _c('td',{key:col,class:{ selected: _vm.curTd === row + ',' + col },on:{"click":function($event){return _vm.onClick(row, col)}}},[(_vm.canEdit && _vm.curTd === row + ',' + col)?_c('el-input',{directives:[{name:"focus",rawName:"v-focus"}],on:{"blur":_vm.onBlur},model:{value:(_vm.tableData[row][col]),callback:function ($$v) {_vm.$set(_vm.tableData[row], col, $$v)},expression:"tableData[row][col]"}}):_c('span',[_vm._v(_vm._s(e))])],1)}),0)}),0)]),_c('div',[_c('button',{on:{"click":_vm.addRow}},[_vm._v("添加新行")]),_c('button',{on:{"click":_vm.addCol}},[_vm._v("添加新列")]),_c('button',{on:{"click":_vm.deleteRow}},[_vm._v("删除行")]),_c('button',{on:{"click":_vm.deleteCol}},[_vm._v("删除列")])])])
}
var staticRenderFns = []

export { render, staticRenderFns }