var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:"svg-star-container"},[_c('svg',{attrs:{"version":"1.1","baseProfile":"full","xmlns":"http://www.w3.org/2000/svg"}},[_c('polygon',{ref:"star",attrs:{"points":_vm.points,"stroke":_vm.element.style.borderColor,"fill":_vm.element.style.backgroundColor,"stroke-width":"1"}})]),_c('v-text',{attrs:{"prop-value":_vm.element.propValue,"element":_vm.element}})],1)
}
var staticRenderFns = []

export { render, staticRenderFns }