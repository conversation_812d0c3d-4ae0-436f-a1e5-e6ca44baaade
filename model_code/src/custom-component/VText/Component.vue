var render = function render(){var _vm=this,_c=_vm._self._c;return (_vm.editMode == 'edit')?_c('div',{staticClass:"v-text",on:{"keydown":_vm.handleKeydown,"keyup":_vm.handleKeyup}},[_c('div',{ref:"text",class:{ 'can-edit': _vm.canEdit },style:({ verticalAlign: _vm.element.style.verticalAlign, padding: _vm.element.style.padding + 'px' }),attrs:{"contenteditable":_vm.canEdit,"tabindex":"0"},domProps:{"innerHTML":_vm._s(_vm.element.propValue)},on:{"dblclick":_vm.setEdit,"paste":_vm.clearStyle,"mousedown":_vm.handleMousedown,"blur":_vm.handleBlur,"input":_vm.handleInput}})]):_c('div',{staticClass:"v-text preview"},[_c('div',{style:({ verticalAlign: _vm.element.style.verticalAlign, padding: _vm.element.style.padding + 'px' }),domProps:{"innerHTML":_vm._s(_vm.element.propValue)}})])
}
var staticRenderFns = []

export { render, staticRenderFns }