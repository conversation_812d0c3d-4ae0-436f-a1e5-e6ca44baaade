var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:"group"},[_c('div',_vm._l((_vm.propValue),function(item){return _c(item.component,{key:item.id,tag:"component",staticClass:"component",style:(item.groupStyle),attrs:{"id":'component' + item.id,"prop-value":item.propValue,"element":item,"request":item.request}})}),1)])
}
var staticRenderFns = []

export { render, staticRenderFns }