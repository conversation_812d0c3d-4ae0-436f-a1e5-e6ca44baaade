var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:"attr-list"},[_c('CommonAttr'),_c('el-form',[_c('el-form-item',{attrs:{"label":"镜像翻转"}},[_c('div',{staticStyle:{"clear":"both"}},[_c('el-checkbox',{attrs:{"label":"horizontal"},model:{value:(_vm.curComponent.propValue.flip.horizontal),callback:function ($$v) {_vm.$set(_vm.curComponent.propValue.flip, "horizontal", $$v)},expression:"curComponent.propValue.flip.horizontal"}},[_vm._v("水平翻转")]),_c('el-checkbox',{attrs:{"label":"vertical"},model:{value:(_vm.curComponent.propValue.flip.vertical),callback:function ($$v) {_vm.$set(_vm.curComponent.propValue.flip, "vertical", $$v)},expression:"curComponent.propValue.flip.vertical"}},[_vm._v("垂直翻转")])],1)])],1)],1)
}
var staticRenderFns = []

export { render, staticRenderFns }