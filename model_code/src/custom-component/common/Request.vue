var render = function render(){var _vm=this,_c=_vm._self._c;return _c('el-collapse-item',{staticClass:"request-container",attrs:{"title":"数据来源（预览生效）","name":"request"}},[_c('el-form',[_c('el-form-item',{attrs:{"label":"请求地址"}},[_c('el-input',{on:{"blur":_vm.validateURL},model:{value:(_vm.request.url),callback:function ($$v) {_vm.$set(_vm.request, "url", (typeof $$v === 'string'? $$v.trim(): $$v))},expression:"request.url"}},[_c('template',{slot:"prepend"},[_vm._v("HTTPS://")])],2)],1),_c('el-form-item',{attrs:{"label":"请求方法"}},[_c('el-select',{model:{value:(_vm.request.method),callback:function ($$v) {_vm.$set(_vm.request, "method", $$v)},expression:"request.method"}},_vm._l((_vm.methodOptions),function(item){return _c('el-option',{key:item,attrs:{"label":item,"value":item}})}),1)],1),_c('el-form-item',{attrs:{"label":"请求参数"}},[_c('el-select',{attrs:{"placeholder":"参数类型"},on:{"change":_vm.onChnage},model:{value:(_vm.request.paramType),callback:function ($$v) {_vm.$set(_vm.request, "paramType", $$v)},expression:"request.paramType"}},_vm._l((_vm.dataOptions),function(item){return _c('el-option',{key:item,attrs:{"label":item,"value":item}})}),1),(_vm.request.paramType === 'array')?_c('div',{staticClass:"param-container"},[_c('p',[_vm._v("数据项")]),_vm._l((_vm.request.data),function(item,index){return _c('div',{key:index,staticClass:"div-delete"},[_c('el-input',{attrs:{"placeholder":"请输入参数值"},model:{value:(_vm.request.data[index]),callback:function ($$v) {_vm.$set(_vm.request.data, index, $$v)},expression:"request.data[index]"}}),_c('span',{staticClass:"iconfont icon-shanchu",on:{"click":function($event){return _vm.deleteData(index)}}})],1)}),_c('el-button',{on:{"click":_vm.addArrayData}},[_vm._v("添加")])],2):(_vm.request.paramType === 'string' || _vm.request.paramType === 'object')?_c('div',{staticClass:"param-container"},[_c('p',[_vm._v("数据项")]),_vm._l((_vm.request.data),function(item,index){return _c('div',{key:index,staticClass:"param-object-container"},[_c('el-input',{attrs:{"placeholder":"参数名"},model:{value:(item[0]),callback:function ($$v) {_vm.$set(item, 0, $$v)},expression:"item[0]"}}),_c('el-input',{attrs:{"placeholder":"参数值"},model:{value:(item[1]),callback:function ($$v) {_vm.$set(item, 1, $$v)},expression:"item[1]"}}),_c('span',{staticClass:"iconfont icon-shanchu",on:{"click":function($event){return _vm.deleteData(index)}}})],1)}),_c('el-button',{on:{"click":_vm.addData}},[_vm._v("添加")])],2):_vm._e()],1),_c('el-form-item',{attrs:{"label":"定时触发"}},[_c('el-switch',{model:{value:(_vm.request.series),callback:function ($$v) {_vm.$set(_vm.request, "series", $$v)},expression:"request.series"}}),(_vm.request.series)?[_c('p',[_vm._v("触发间隔（毫秒）")]),_c('el-input',{attrs:{"type":"number"},model:{value:(_vm.request.time),callback:function ($$v) {_vm.$set(_vm.request, "time", $$v)},expression:"request.time"}}),_c('p',[_vm._v("触发次数（0 为无限）")]),_c('el-input',{attrs:{"type":"number"},model:{value:(_vm.request.requestCount),callback:function ($$v) {_vm.$set(_vm.request, "requestCount", $$v)},expression:"request.requestCount"}})]:_vm._e()],2)],1)],1)
}
var staticRenderFns = []

export { render, staticRenderFns }