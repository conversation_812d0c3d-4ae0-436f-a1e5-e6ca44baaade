var render = function render(){var _vm=this,_c=_vm._self._c;return _c('el-collapse-item',{staticClass:"linkage-container",attrs:{"title":"组件联动（预览生效）","name":"linkage"}},[_c('el-form',[_vm._l((_vm.linkage.data),function(item,index){return _c('div',{key:index,staticClass:"linkage-component"},[_c('div',{staticClass:"div-guanbi",on:{"click":function($event){return _vm.deleteLinkageData(index)}}},[_c('span',{staticClass:"iconfont icon-guanbi"})]),_c('el-select',{staticClass:"testtest",attrs:{"placeholder":"请选择联动组件"},model:{value:(item.id),callback:function ($$v) {_vm.$set(item, "id", $$v)},expression:"item.id"}},_vm._l((_vm.componentData),function(component,i){return _c('el-option',{key:component.id,attrs:{"value":component.id,"label":component.label}},[_c('div',{on:{"mouseenter":function($event){return _vm.onEnter(i)},"mouseout":function($event){return _vm.onOut(i)}}},[_vm._v(" "+_vm._s(component.label)+" ")])])}),1),_c('el-select',{attrs:{"placeholder":"请选择监听事件"},model:{value:(item.event),callback:function ($$v) {_vm.$set(item, "event", $$v)},expression:"item.event"}},_vm._l((_vm.eventOptions),function(e){return _c('el-option',{key:e.value,attrs:{"value":e.value,"label":e.label}})}),1),_c('p',[_vm._v("事件触发时，当前组件要修改的属性")]),_vm._l((item.style),function(e,i){return _c('div',{key:i,staticClass:"attr-container"},[_c('el-select',{on:{"change":function($event){e.value = ''}},model:{value:(e.key),callback:function ($$v) {_vm.$set(e, "key", $$v)},expression:"e.key"}},_vm._l((Object.keys(_vm.curComponent.style)),function(attr){return _c('el-option',{key:attr,attrs:{"value":attr,"label":_vm.styleMap[attr]}})}),1),(_vm.isIncludesColor(e.key))?_c('el-color-picker',{attrs:{"show-alpha":""},model:{value:(e.value),callback:function ($$v) {_vm.$set(e, "value", $$v)},expression:"e.value"}}):(_vm.selectKey.includes(e.key))?_c('el-select',{model:{value:(e.value),callback:function ($$v) {_vm.$set(e, "value", $$v)},expression:"e.value"}},_vm._l((_vm.optionMap[e.key]),function(option){return _c('el-option',{key:option.value,attrs:{"label":option.label,"value":option.value}})}),1):_c('el-input',{attrs:{"type":"number","placeholder":"请输入"},model:{value:(e.value),callback:function ($$v) {_vm.$set(e, "value", _vm._n($$v))},expression:"e.value"}}),_c('span',{staticClass:"iconfont icon-shanchu",on:{"click":function($event){return _vm.deleteData(item.style, i)}}})],1)}),_c('el-button',{on:{"click":function($event){return _vm.addAttr(item.style)}}},[_vm._v("添加属性")])],2)}),_c('el-button',{staticStyle:{"margin-bottom":"10px"},on:{"click":_vm.addComponent}},[_vm._v(" 添加组件 ")]),_c('p',[_vm._v("过渡时间（秒）")]),_c('el-input',{staticClass:"input-duration",attrs:{"placeholder":"请输入"},model:{value:(_vm.linkage.duration),callback:function ($$v) {_vm.$set(_vm.linkage, "duration", $$v)},expression:"linkage.duration"}})],2)],1)
}
var staticRenderFns = []

export { render, staticRenderFns }