var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:"v-common-attr",on:{"mousedown":function($event){return _vm.setInitial(_vm.curComponent.style)}}},[_c('el-collapse',{attrs:{"accordion":""},on:{"change":_vm.onChange},model:{value:(_vm.activeName),callback:function ($$v) {_vm.activeName=$$v},expression:"activeName"}},[_c('el-collapse-item',{attrs:{"title":"通用样式","name":"style"}},[_c('el-form',_vm._l((_vm.styleKeys),function({ key, label },index){return _c('el-form-item',{key:index,attrs:{"label":label}},[(_vm.isIncludesColor(key))?_c('el-color-picker',{attrs:{"show-alpha":""},model:{value:(_vm.curComponent.style[key]),callback:function ($$v) {_vm.$set(_vm.curComponent.style, key, $$v)},expression:"curComponent.style[key]"}}):(_vm.selectKey.includes(key))?_c('el-select',{model:{value:(_vm.curComponent.style[key]),callback:function ($$v) {_vm.$set(_vm.curComponent.style, key, $$v)},expression:"curComponent.style[key]"}},_vm._l((_vm.optionMap[key]),function(item){return _c('el-option',{key:item.value,attrs:{"label":item.label,"value":item.value}})}),1):(key == 'fontSize' && _vm.curComponent.component == 'VText')?_c('el-input',{attrs:{"type":"number"},on:{"input":function($event){return _vm.setFontSize(_vm.curComponent)}},model:{value:(_vm.curComponent.style[key]),callback:function ($$v) {_vm.$set(_vm.curComponent.style, key, _vm._n($$v))},expression:"curComponent.style[key]"}}):_c('el-input',{attrs:{"type":"number"},model:{value:(_vm.curComponent.style[key]),callback:function ($$v) {_vm.$set(_vm.curComponent.style, key, _vm._n($$v))},expression:"curComponent.style[key]"}})],1)}),1)],1),(_vm.curComponent.device_attr)?_c('el-collapse-item',{attrs:{"title":"设备属性","name":"style1"}},[_c('el-form',_vm._l((_vm.deviceKeys),function({ key, label },index){return _c('el-form-item',{key:index,attrs:{"label":label}},[(_vm.isIncludesColor(key))?_c('el-color-picker',{attrs:{"show-alpha":""},model:{value:(_vm.curComponent.device_attr[key]),callback:function ($$v) {_vm.$set(_vm.curComponent.device_attr, key, $$v)},expression:"curComponent.device_attr[key]"}}):(_vm.selectKey.includes(key))?_c('el-select',{model:{value:(_vm.curComponent.device_attr[key]),callback:function ($$v) {_vm.$set(_vm.curComponent.device_attr, key, $$v)},expression:"curComponent.device_attr[key]"}},_vm._l((_vm.optionMap[key]),function(item){return _c('el-option',{key:item.value,attrs:{"label":item.label,"value":item.value}})}),1):(key == 'fontSize' && _vm.curComponent.component == 'VText')?_c('el-input',{attrs:{"type":"text"},on:{"input":function($event){return _vm.setFontSize(_vm.curComponent)}},model:{value:(_vm.curComponent.device_attr[key]),callback:function ($$v) {_vm.$set(_vm.curComponent.device_attr, key, _vm._n($$v))},expression:"curComponent.device_attr[key]"}}):_c('el-input',{attrs:{"type":"text"},model:{value:(_vm.curComponent.device_attr[key]),callback:function ($$v) {_vm.$set(_vm.curComponent.device_attr, key, _vm._n($$v))},expression:"curComponent.device_attr[key]"}})],1)}),1)],1):_vm._e(),(_vm.curComponent.request)?_c('Request'):_vm._e(),(_vm.curComponent.linkage)?_c('Linkage'):_vm._e()],1)],1)
}
var staticRenderFns = []

export { render, staticRenderFns }