import { render, staticRenderFns } from "./Previews.vue?vue&type=template&id=edf5a73e&scoped=true"
import script from "./Previews.vue?vue&type=script&lang=js"
export * from "./Previews.vue?vue&type=script&lang=js"
import style0 from "./Previews.vue?vue&type=style&index=0&id=edf5a73e&prod&lang=scss&scoped=true"


/* normalize component */
import normalizer from "!../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js"
var component = normalizer(
  script,
  render,
  staticRenderFns,
  false,
  null,
  "edf5a73e",
  null
  
)

export default component.exports