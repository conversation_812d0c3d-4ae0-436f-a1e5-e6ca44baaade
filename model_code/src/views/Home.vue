<template>
  <div :class="['home', { 'dark': isDarkMode }]">
    <!-- 工具栏 -->
    <Toolbar />

    <!-- 主要内容区域 -->
    <main class="main-content">
      <!-- 左侧组件库 -->
      <section :class="['left-panel', { 'active': leftList, 'inactive': !leftList }]">
        <ComponentList />
      </section>

      <!-- 左侧面板切换按钮 -->
      <el-button
        class="panel-toggle left-toggle"
        :icon="leftList ? 'el-icon-arrow-left' : 'el-icon-arrow-right'"
        @click="toggleLeftPanel"
        size="small"
      />

      <!-- 中央编辑区域 -->
      <section class="center-panel" :style="centerPanelStyle">
        <div class="editor-container">
          <Editor />
        </div>
      </section>

      <!-- 右侧属性面板 -->
      <section :class="['right-panel', { 'active': rightList, 'inactive': !rightList }]">
        <AttrList />
      </section>

      <!-- 右侧面板切换按钮 -->
      <el-button
        class="panel-toggle right-toggle"
        :icon="rightList ? 'el-icon-arrow-right' : 'el-icon-arrow-left'"
        @click="toggleRightPanel"
        size="small"
      />
    </main>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import Toolbar from '@/components/Toolbar.vue'
import ComponentList from '@/components/ComponentList.vue'
import Editor from '@/components/Editor.vue'
import AttrList from '@/components/AttrList.vue'

export default {
  name: 'Home',
  components: {
    Toolbar,
    ComponentList,
    Editor,
    AttrList
  },
  data() {
    return {
      leftList: true,
      rightList: true
    }
  },
  computed: {
    ...mapState(['isDarkMode']),

    centerPanelStyle() {
      let marginLeft = this.leftList ? '280px' : '10px'
      let marginRight = this.rightList ? '280px' : '10px'

      return {
        marginLeft,
        marginRight
      }
    }
  },
  methods: {
    toggleLeftPanel() {
      this.leftList = !this.leftList
    },

    toggleRightPanel() {
      this.rightList = !this.rightList
    }
  }
}
</script>

<style scoped>
.home {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}

.main-content {
  flex: 1;
  display: flex;
  position: relative;
  overflow: hidden;
}

.left-panel {
  width: 280px;
  background: #fff;
  border-right: 1px solid #e4e7ed;
  transition: all 0.3s ease;
  z-index: 10;
}

.left-panel.inactive {
  width: 0;
  overflow: hidden;
}

.right-panel {
  width: 280px;
  background: #fff;
  border-left: 1px solid #e4e7ed;
  transition: all 0.3s ease;
  z-index: 10;
}

.right-panel.inactive {
  width: 0;
  overflow: hidden;
}

.center-panel {
  flex: 1;
  transition: all 0.3s ease;
  position: relative;
}

.editor-container {
  width: 100%;
  height: 100%;
}

.panel-toggle {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  z-index: 20;
  border-radius: 0;
  padding: 8px 4px;
}

.left-toggle {
  left: 280px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.left-panel.inactive + .left-toggle {
  left: 0;
}

.right-toggle {
  right: 280px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.right-panel.inactive ~ .right-toggle {
  right: 0;
}

/* 暗色主题 */
.home.dark {
  background: #1a1a1a;
}

.home.dark .left-panel,
.home.dark .right-panel {
  background: #2d2d2d;
  border-color: #404040;
}
</style>