var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{class:!_vm.isDarkMode ? 'home' : 'home dark'},[_c('Toolbar'),_c('main',[_c('section',{class:_vm.leftList ? 'left active' : 'left inactive'},[_c('ComponentList'),_c('RealTimeComponentList')],1),_c('el-button',{staticClass:"btn show-list left-btn",attrs:{"title":"show-list-btn","icon":_vm.leftList ? 'el-icon-arrow-left' : 'el-icon-arrow-right'},on:{"click":_vm.isShowLeft}}),_c('section',{staticClass:"center",style:(_vm.rightList ? 'margin-right:288px' : 'margin-right:10px')},[_c('div',{staticClass:"content",on:{"drop":_vm.handleDrop,"dragover":_vm.handleDragOver,"mousedown":_vm.handleMouseDown,"mouseup":_vm.deselectCurComponent}},[_c('Editor')],1)]),_c('section',{class:_vm.rightList ? 'right active' : 'right inactive'},[(_vm.curComponent)?_c('el-tabs',{model:{value:(_vm.activeName),callback:function ($$v) {_vm.activeName=$$v},expression:"activeName"}},[_c('el-tab-pane',{attrs:{"label":"属性","name":"attr"}},[_c(_vm.curComponent.component + 'Attr',{tag:"component"})],1),_c('el-tab-pane',{staticStyle:{"padding-top":"20px"},attrs:{"label":"动画","name":"animation"}},[_c('AnimationList')],1),_c('el-tab-pane',{staticStyle:{"padding-top":"20px"},attrs:{"label":"事件","name":"events"}},[_c('EventList')],1)],1):_c('CanvasAttr')],1),_c('el-button',{staticClass:"btn show-list right-btn",attrs:{"title":"show-list-btn","icon":_vm.rightList ? 'el-icon-arrow-right' : 'el-icon-arrow-left'},on:{"click":_vm.isShowRight}})],1)],1)
}
var staticRenderFns = []

export { render, staticRenderFns }