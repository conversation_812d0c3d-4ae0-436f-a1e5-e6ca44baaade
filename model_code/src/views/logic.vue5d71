import { render, staticRenderFns } from "./logic.vue?vue&type=template&id=7565f6fe"
import script from "./logic.vue?vue&type=script&lang=js"
export * from "./logic.vue?vue&type=script&lang=js"
import style0 from "./logic.vue?vue&type=style&index=0&id=7565f6fe&prod&lang=scss"


/* normalize component */
import normalizer from "!../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js"
var component = normalizer(
  script,
  render,
  staticRenderFns,
  false,
  null,
  null,
  null
  
)

export default component.exports