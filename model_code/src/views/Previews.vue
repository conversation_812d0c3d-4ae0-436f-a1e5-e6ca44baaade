var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:"bg"},[_c('div',{on:{"click":function($event){return _vm.login()}}},[_vm._v("111")]),_c('div',{on:{"click":function($event){return _vm.login3()}}},[_vm._v("获取数据")]),_vm._m(0),_c('div',{staticClass:"main-all"},[_c('div',{staticClass:"main"},[_vm._l((_vm.ComponentListTo.collectorList),function(item,index){return _c('div',{key:index,staticClass:"cjq"},[_c('div',{staticClass:"inverter_info"},[_c('img',{staticClass:"inverter_img",attrs:{"src":require("@/assets/bzq.png")}}),_c('span',{staticClass:"inverter_text"},[_vm._v(_vm._s(item.cloudName))])])])}),_c('div',{staticClass:"groupList"},_vm._l((_vm.ComponentListTo.groupList),function(item,index){return _c('div',{key:index,staticClass:"inverter_components"},[_c('div',[_c('img',{staticClass:"inverter",attrs:{"src":item.groupName == '常规组串'
                  ? require('@/assets/components.png')
                  : require('@/assets/Intelligent.png')}}),_c('div',{staticClass:"groupList_data"},_vm._l((_vm.ComponentListTo.data),function(item,index){return _c('div',{key:index,staticClass:"classify-cell-recom"},[_c('img',{staticClass:"inverters",attrs:{"src":require("@/assets/component_lx.png")}})])}),0)])])}),0)],2)])])
}
var staticRenderFns = [function (){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:"maintop"},[_c('div',{staticClass:"main-top"},[_c('div',[_c('span',[_vm._v("组件百分比")]),_c('img',{staticClass:"triangle",attrs:{"src":require("@/assets/triangle.png")}})]),_c('div',[_c('span',[_vm._v("日发电量(度)")]),_c('img',{staticClass:"triangle",attrs:{"src":require("@/assets/triangle.png")}})]),_c('div',[_c('span',[_vm._v("12.837")]),_c('span',{staticClass:"triangle_num"},[_vm._v("KWh")])])]),_c('div',{staticClass:"main-tag"},[_c('div',{staticClass:"triangle-t"},[_c('div',{staticClass:"triangle—mian"},[_c('img',{staticClass:"triangle-icon",attrs:{"src":require("@/assets/total.png")}}),_c('span',[_vm._v("总计")])]),_c('span',{staticClass:"total_num"},[_vm._v("126")])]),_c('div',{staticClass:"triangle-t"},[_c('div',{staticClass:"triangle—mian"},[_c('img',{staticClass:"triangle-icon",attrs:{"src":require("@/assets/normal.png")}}),_c('span',[_vm._v("正常")])]),_c('span',{staticClass:"total_num"},[_vm._v("126")])]),_c('div',{staticClass:"triangle-t"},[_c('div',{staticClass:"triangle—mian"},[_c('img',{staticClass:"triangle-icon",attrs:{"src":require("@/assets/fault.png")}}),_c('span',[_vm._v("故障")])]),_c('span',{staticClass:"total_num"},[_vm._v("126")])]),_c('div',{staticClass:"triangle-t"},[_c('div',{staticClass:"triangle—mian"},[_c('img',{staticClass:"triangle-icon",attrs:{"src":require("@/assets/abnormal.png")}}),_c('span',[_vm._v("异常")])]),_c('span',{staticClass:"total_num"},[_vm._v("126")])]),_c('div',{staticClass:"triangle-t"},[_c('div',{staticClass:"triangle—mian"},[_c('img',{staticClass:"triangle-icon",attrs:{"src":require("@/assets/offline.png")}}),_c('span',[_vm._v("离线")])]),_c('span',{staticClass:"total_num"},[_vm._v("126")])])])])
}]

export { render, staticRenderFns }