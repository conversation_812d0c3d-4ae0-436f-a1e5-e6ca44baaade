<template>
  <div class="home wh100 flex-column-start-between">
    <div class="icons flex-column-start-between" v-if="path.length < 2">
      <div
        :class="curKey == item.key ? 'active' : ''"
        v-for="item in options"
        :key="item.key"
        @click="() => (curKey = item.key)"
      >
        <img :src="curKey == item.key ? item.icons[1] : item.icons[0]" alt="" />
      </div>
    </div>
    <div
      class="station-info"
      v-if="path.length < 2 && curKey != 2"
    >
    <div class="info_item item_1">
      <div class="title">
        <span class="line"></span>
        <span class="text">电站状态统计</span>
      </div>
      <div class="content">
        <div class="content_item line">
          <img src="../../assets/images/index/icon_01.png" alt="">
          <div class="text">
            <div class="tip">总计</div>
            <div class="num">{{ 
            stationCount.normalCount +
            stationCount.abnormalCount +
            stationCount.offlineCount 
             }}</div>
          </div>
        </div>
        <div class="content_item">
          <img src="../../assets/images/index/icon_02.png" alt="">
          <div class="text">
            <div class="tip">正常</div>
            <div class="num">{{ stationCount.normalCount || 0 }}</div>
          </div>
        </div>
        <div class="content_item">
          <img src="../../assets/images/index/icon_03.png" alt="">
          <div class="text">
            <div class="tip">故障</div>
            <div class="num">{{ stationCount.abnormalCount || 0 }}</div>
          </div>
        </div>
        <div class="content_item">
          <img src="../../assets/images/index/icon_04.png" alt="">
          <div class="text">
            <div class="tip">离线</div>
            <div class="num">{{ stationCount.offlineCount || 0 }}</div>
          </div>
        </div>
      </div>
    </div>
    <div class="info_item item_2">
      <div class="title">
        <span class="line"></span>
        <span class="text">运维信息统计</span>
      </div>
      <div class="content">
        <div class="content_item line">
          <img src="../../assets/images/index/icon_05.png" alt="">
          <div class="text">
            <div class="tip">总计</div>
            <div class="num">{{ warnCount || 0 }}</div>
          </div>
        </div>
        <div class="content_item line">
          <img src="../../assets/images/index/icon_06.png" alt="">
          <div class="text">
            <div class="tip">已处理</div>
            <div class="num">0</div>
          </div>
        </div>
        <div class="content_item line">
          <img src="../../assets/images/index/icon_07.png" alt="">
          <div class="text">
            <div class="tip">待处理</div>
            <div class="num">{{ warnCount || 0 }}</div>
          </div>
        </div>
        <div class="content_item">
          <img src="../../assets/images/index/icon_10.png" alt="">
          <div class="text">
            <div class="tip">一级警告</div>
            <div class="num">0</div>
          </div>
        </div>
      </div>
    </div>
    <div class="info_item item_3">
      <div class="address">
        <div class="left">
          <a-icon type="global" />
          <span>嘉 兴</span>
        </div>
        <div class="right">2025/04/10 16:53 </div>
      </div>
      <div class="address_inner">
        <div class="left">
          <img src="../../assets/images/index/icon_08.png" alt="">
          <div class="text">多云10~21℃</div>
        </div>
        <div class="right">
          <p>空气质量</p>
          <p>空气湿度</p>
          <p>日温度</p>
          <p>日湿度</p>
        </div>
      </div>
    </div>
    </div>

    <a-breadcrumb class="p16">
      <a-breadcrumb-item class="cursor">
        <img
          src="@/assets/images/xitong.png"
          alt=""
          style="width: 18px; height: 18px"
        />
      </a-breadcrumb-item>
      <a-breadcrumb-item
        class="cursor"
        @click="() => (path = ['/system-view'])"
        >{{ locales?.xitongshitu }}</a-breadcrumb-item
      >
      <a-breadcrumb-item
        class="cursor"
        v-if="path[1] && path[1] == '/over-view'"
        >{{ locales.gailan }}</a-breadcrumb-item
      >
      <a-breadcrumb-item
        class="cursor"
        v-if="path[1] && path[1] == '/physical-view'"
        >{{ locales.wulishitu }}</a-breadcrumb-item
      >
      <a-breadcrumb-item
        class="cursor"
        v-if="path[1] && path[1] == '/logic-view'"
        >{{ locales.luojishitu }}</a-breadcrumb-item
      >
    </a-breadcrumb>
    <div
      class="charts-info plr16 w100 flex-row-center-between"
      v-if="path.length < 2 && curKey == 3"
    >
      <div class="left flex1" style="height: 452px">
        <Line :chartsData="chartsData" />
      </div>
      <div class="right h100 flex1 ml16 flex-column-start-between">
        <div class="top w100 flex-column">
          <div class="title flex-row-start-between nowrap">
            {{ locales.dangridianzhanpaiming
            }}<span
              class="nowrap cursor"
              v-if="barData.length > 3"
              @click="() => (openWBarModal = true)"
              >{{ locales.gengduo }}</span
            >
          </div>
          <!-- 横向柱状图 WBar -->
          <div class="box w100 mt16" style="height: calc(100% - 44px)">
            <WBar :legend="legend.slice(0, 3)" :data="barData.slice(0, 3)" />
          </div>
        </div>
        <div class="bottom w100 mt16">
          <div class="title">{{ locales.shehuigongxian }}</div>
          <div class="cards w100 flex-row-start-between">
            <div
              class="card-item flex1 flex-column-start-center"
              style="margin-right: 24px"
            >
              <div class="flex-row-end-start">
                <div class="value">29.36</div>
                <div class="unit">T</div>
              </div>
              <div class="label">减排CO2总量</div>
            </div>
            <div class="card-item flex1 flex-column-start-center">
              <div class="flex-row-end-start">
                <div class="value">49.36</div>
                <div class="unit">T</div>
              </div>
              <div class="label">节约标准煤总量</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 首页地图 -->
    <Map v-if="path.length < 2 && curKey == 2" />
    <!-- 表格 -->
    <div
      class="table-container w100 plr16"
      v-if="path.length < 2 && curKey == 1"
    >
      <div class="table-part wh100">
        <Search :searchData="searchData" @handleEvent="handleTableFilter" />
        <div class="btns flex-row-center-start">
          <button class="mr16" @click="() => handleViewLink('/over-view')">
            {{ locales.gailan }}
          </button>
          <button class="mr16" @click="() => handleViewLink('/physical-view')">
            {{ locales.wulishitu }}
          </button>
          <button @click="() => handleViewLink('/logic-view')">
            {{ locales.luojishitu }}
          </button>
        </div>
        <Table
          class="flex1"
          :refreshTableData="() => handleQuery(queryData)"
          :isLoading="tableData.isLoading"
          :columns="tableData.columns"
          :dataSource="tableData.dataSource"
          :page="tableData.page"
          :pageSize="tableData.pageSize"
          :total="tableData.total"
          :selectedRowKeys="tableData.selectedRowKeys"
          @pageChange="handlePageChange"
          @emitRowCheckboxChange="handleRowCheckboxChange"
        />
      </div>
    </div>
    <!-- 概览 -->
    <!-- <OverView v-if="path[1] && path[1] == '/over-view'"></OverView> -->
    <!-- 物理视图 -->
    <iframe
      :src="
        logicSrc +
        '/model/#/logic?id=' +
        (tableData.selectedRowKeys[0] || '') +
        '&name=' +
        (tableData.dataSource.find((e) => e.id == tableData.selectedRowKeys[0])
          ?.systemName || '')
      "
      frameborder="0"
      class="w100 flex1"
      v-if="path[1] && path[1] == '/physical-view'"
    ></iframe>
    <!-- 逻辑视图 -->
    <iframe
      :src="
        logicSrc + '/model/#/preview?id=' + tableData.selectedRowKeys[0] || ''
      "
      frameborder="0"
      class="w100 flex1"
      v-if="path[1] && path[1] == '/logic-view'"
    ></iframe>
    <!-- 电站电量bar图弹窗 -->
    <a-modal
      v-model:open="openWBarModal"
      :title="locales.dangridianzhanpaiming"
      class="station-modal"
    >
      <WBar :legend="legend" :data="barData" />
    </a-modal>
  </div>
</template>

<script setup>
import { ref, defineAsyncComponent, onBeforeMount, computed } from "vue";
import tableMixin from "@/mixins/table.js";
import { getDataList, queryWarningTypeCount, groupByKwh } from "@/api/list.js";
delete require.cache[require.resolve("@/db.js")];
const { stationColumn1 } = require("@/db.js");
import Pie from "../../components/Pie.vue";
import WBar from "../../components/WBar.vue";
import Line from "../../components/Line.vue";
import { message } from "ant-design-vue";
const Map = defineAsyncComponent(() => import("../components/Map/index.vue"));
// const OverView = defineAsyncComponent(() =>
//   import("./components/overview.vue")
// );
const Search = defineAsyncComponent(() =>
  import("../components/Search/index.vue")
);
const Table = defineAsyncComponent(() =>
  import("../components/Table/index.vue")
);

let { tableData, handlePageChange, handleRowCheckboxChange } = tableMixin();

const BASE_URL =
  process.env.NODE_ENV === "production"
    ? window.location.host
    : "***********:39090";
const networkType = window.location.protocol;
const logicSrc = networkType + "//" + BASE_URL;

let locales = computed(
  () => JSON.parse(sessionStorage.getItem("locales")) || {}
);

let path = ref(["/system-view"]);
let curKey = ref(1);
const options = [
  {
    key: 1,
    icons: [
      require("../../assets/images/s1.png"),
      require("../../assets/images/s1-active.png"),
    ],
  },
  {
    key: 2,
    icons: [
      require("../../assets/images/s2.png"),
      require("../../assets/images/s2-active.png"),
    ],
  },
  {
    key: 3,
    icons: [
      require("../../assets/images/s3.png"),
      require("../../assets/images/s3-active.png"),
    ],
  },
];
let kpis = [
  {
    icon: require("../../assets/images/gonglv.png"),
    label: "当前功率",
    value: "7.82",
    unit: "kW",
  },
  {
    icon: require("../../assets/images/ri.png"),
    label: "当日发电量",
    value: "1.82",
    unit: "度",
  },
  {
    icon: require("../../assets/images/shouyi.png"),
    label: "当日收益",
    value: "2.45",
    unit: "元",
  },
  {
    icon: require("../../assets/images/yue.png"),
    label: "当月发电量",
    value: "0.82",
    unit: "万度",
  },
  {
    icon: require("../../assets/images/eding.png"),
    label: "逆变器额定功率",
    value: "32.00",
    unit: "kW",
  },
  {
    icon: require("../../assets/images/nian.png"),
    label: "年度发电量",
    value: "21.52",
    unit: "万度",
  },
];

let chartsData = {
  dataX: [
    "00:00",
    "01:00",
    "02:00",
    "03:00",
    "04:00",
    "05:00",
    "06:00",
    "07:00",
  ],
  dataS: [2, 8, 10, 13, 12, 21, 35, 40],
  data: [
    { time: "00:00", value: 2 },
    { time: "01:00", value: 8 },
  ],
};

async function handleQuery(data) {
  let res = await getDataList("powerstation/queryPowerStationList.web", {
    pageNo: tableData.page,
    pageSize: tableData.pageSize,
    systemName: data?.systemName || "",
    createTimeStart: data?.createTime?.[0] || "",
    createTimeEnd: data?.createTime?.[1] || "",
  });
  tableData.isLoading = false;
  if (res?.data) {
    let { reModel, code, count } = res.data;
    if (code === 0 && reModel) {
      tableData.total = count;
      tableData.dataSource = reModel.data;
    }
  }
}

let openWBarModal = ref(false);

let warnCount = ref(0);
let stationCount = ref({});
async function getWarningTypeCount() {
  let res = await queryWarningTypeCount();
  if (res?.data?.code === 0) {
    const { offlineCount, normalCount, abnormalCount, typeStats } =
      res.data.reModel;
    stationCount.value = { offlineCount, normalCount, abnormalCount };
    let arr =
      typeStats
        ?.filter(
          (e) => e.warningType == 1 || e.warningType == 2 || e.warningType == 6
        )
        ?.map((v) => v.warningCount) || [];
    warnCount.value = arr.reduce((acc, curr) => (acc || 0) + curr, 0) || 0;
  }
}

let legend = ref([]);
let barData = ref([]);
// 当日电站排名（等价发电时）
async function getGroupByKwh() {
  legend.value = [];
  barData.value = [];
  let res = await groupByKwh();
  if (res?.data?.code === 0 && res.data.reModel?.length) {
    res.data.reModel
      .sort((a, b) => b.kwh - a.kwh)
      .forEach((e) => {
        legend.value.push(e.powerStationName);
        barData.value.push(e.kwh);
      });
  }
}

onBeforeMount(async () => {
  getWarningTypeCount();
  tableData.columns = stationColumn1;
  await handleQuery();
  getGroupByKwh();
});

const searchData = computed(() => [
  {
    label: locales.value.dianzhanmingcheng,
    key: "systemName",
    value: undefined,
    type: "input",
  },
  {
    label: locales.value.chaungjianshijian,
    key: "createTime",
    value: undefined,
    type: "rangeTime",
  },
]);

let queryData = ref(null);
async function handleTableFilter(v) {
  queryData.value = v.data;
  tableData.page = 1;
  if (v.key == "search") {
    handleQuery(v.data);
  } else if (v.key == "reset") {
    handleQuery(v.data);
  }
}

// 跳转物理/逻辑视图
function handleViewLink(secondPath) {
  if (tableData.selectedRowKeys.length != 1)
    return message.info(locales.value.zhengquexuanzecaozuoxiang);
  else path.value.push(secondPath);
}
</script>

<style lang="less" scoped>
.home {
  position: relative;
  .icons {
    position: absolute;
    top: 38px;
    right: 0;
    width: 52px;
    height: 144px;
    z-index: 1000;

    > div {
      position: relative;
      width: 32px;
      height: 32px;
      cursor: pointer;
      > img {
        width: 32px;
        height: 32px;
      }
      &.active {
        &::before {
          position: absolute;
          top: 10px;
          right: -18px;
          content: "";
          border-right: 8px solid #fff;
          border-top: 6px solid transparent;
          border-bottom: 6px solid transparent;
        }
      }
    }
  }
  .station-info {
    width: 100%;
    height: 160px;
    display: flex;
    justify-content: space-between;
    .info_item{
      height: 100%;
      background: #fff;
      border-radius: 15px;
      background: rgba(255, 255, 255, 100);
      box-shadow: 6px 6px 54px  rgba(0, 0, 0, 0.05);
      padding: 30px 40px;
      .title{
        width: 100%;
        height: 27px;
        display: flex;
        align-items: center;
        .line{
          display: inline-block;
          width: 8px;
          height: 27px;
          background: rgba(230, 0, 18, 1);
          border-radius: 20px;
        }
        .text{
          font-size: 24px;
          line-height: 27px;
          color: rgba(56, 56, 56, 1);
          margin-left: 10px;
        }
      }
      .content{
        width: 100%;
        height: 50px;
        display: flex;
        justify-content: space-between;
        .content_item{
          width: 24%;
          height: 100%;
          display: flex;
          margin-top: 14px;
          img{
            width: 50px;
            height: 50px;
            margin-left: 20px;
          }
          .text{
            margin-left: 10px;
            .tip{
              font-size: 16px;
              color: rgba(102, 102, 102, 1);
            }
            .num{
              font-size: 35px;
              font-weight: 700;
              color: rgba(61, 61, 61, 1);
            }
          }
        }
        .line{
          border-right: 1px solid rgba(207, 207, 207, 1);
        }
      }
      .address{
        display: flex;
        justify-content: space-between;
        font-size: 16px;
        font-weight: 600;
        color: rgba(56, 56, 56, 1);
      }
      .address_inner{
        margin-top: 10px;
        display: flex;
        justify-content: space-between;
        .left{
          width: 90px;
          height: 70px;
          img{
            width: 46px;
            height: 46px;
            // margin: 0 auto;
            display: block;
          }
          .text{
            font-size: 14px;
            color: rgba(102, 102, 102, 1);
            margin-top: 5px;
            // text-align: center;
          }
        }
        .right{
          font-size: 14px;
          p{
            margin-bottom: 2px;
          }
        }
      }
    }
    .item_1{
      width: 30%;
    }
    .item_2{
      width: 50%;
    }
    .item_3{
      width: 15%;
      border-radius: 15px;
      background: url(https://img.js.design/assets/img/6577d782a9136f57d8081da6.png);
      box-shadow: 6px 6px 54px  rgba(0, 0, 0, 0.05);
    }
  }
    .station-info1 {
    flex-shrink: 0;
    height: 367px;
    padding-right: 52px;
    background-image: url("../../assets/images/system-bg.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
    width: 100%;
    display: flex;
    justify-content: space-between;
    .title {
      color: #fff;
      font-size: 24px;
      font-weight: 500;
      letter-spacing: 1px;
    }
    .kpi-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      grid-column-gap: 70px;
      grid-row-gap: 20px;
      margin-top: 40px;
      .grid-item {
        > img {
          width: 32px;
          height: 32px;
          margin: 8px 16px 0 0;
        }
        .info {
          .top {
            margin-bottom: 6px;
            .value {
              width: 70px;
              font-size: 24px;
              font-weight: 700;
              letter-spacing: 1px;
              line-height: 28px;
              color: #fff;
              margin-bottom: 2px;
            }
            .unit {
              font-size: 12px;
              font-weight: 500;
              letter-spacing: 1px;
              line-height: 17px;
              color: #a6a6a6;
            }
          }
          .label {
            font-size: 14px;
            font-weight: 500;
            letter-spacing: 1px;
            line-height: 20px;
            color: #808c9d;
          }
        }
      }
    }
    .state-part,
    .alarm-part {
      .detail {
        height: 208px;
        margin-top: 40px;
        .left {
          width: 144px;
          height: 144px;
          margin-right: 80px;
        }
        .right {
          .state-item {
            // position: relative;
            display: flex;
            flex-direction: row;
            align-items: center;
            margin-bottom: 24px;
            &:last-child {
              margin-bottom: 0;
            }
            .type {
              width: 100px;
              font-size: 16.8px;
              line-height: 24px;
              color: #fff;
            }
            .num {
              font-size: 20px;
              font-weight: 700;
              line-height: 26.4px;
              color: #fff;
            }
            .circle {
              width: 16px;
              height: 16px;
              margin-right: 2px;
              margin-bottom: 2px;
              border-radius: 50%;
              background-color: rgba(255, 255, 255, 0.29);
              > div {
                width: 10px;
                height: 10px;
                border-radius: 50%;
              }
            }
            &.normal {
              .circle {
                > div {
                  background-color: #73c764;
                }
              }
            }
            &.fault {
              .circle {
                > div {
                  background-color: #e9443f;
                }
              }
            }
            &.abnormal {
              .circle {
                > div {
                  background-color: #f9be35;
                }
              }
            }
            &.offline {
              .circle {
                > div {
                  background-color: #bcbcbe;
                }
              }
            }
          }
        }
      }
    }
  }
  .charts-info {
    height: 414px;
    overflow: hidden;
    .right {
      .top,
      .bottom {
        height: 214px;
        background-color: #fff;
        padding: 20px 24px 0;
        border-radius: 2px;
        overflow: hidden;
        .title {
          font-size: 18px;
          font-weight: 500;
          line-height: 28px;
          color: #0d0c12;
        }
        .cards {
          margin-top: 12px;
          .card-item {
            height: 110px;
            padding: 0 24px;
            background-image: url("../../assets/images/bg2.png");
            background-size: 100% 100%;
            background-repeat: no-repeat;
            .value {
              font-size: 28px;
              color: #0d0c12;
              line-height: 44px;
            }
            .unit {
              font-size: 18px;
              color: #0d0c12;
              margin-left: 5px;
              line-height: 40px;
            }
            .label {
              font-size: 16px;
              color: #a6a6a6;
              margin-top: 5px;
            }
            &:first-child {
              background-image: url("../../assets/images/bg1.png");
              background-size: 100% 100%;
              background-repeat: no-repeat;
            }
          }
        }
      }
      .top {
        flex-shrink: 0;
      }
    }
  }
  .table-container {
    // height: calc(100% - 421px);
    max-height: 100%;
  }
  .table-part {
    height: 100% !important;
    .btns {
      margin: 12px 0;
      > button {
        min-width: 84px;
        height: 32px;
        border-radius: 2px;
        background: #165dff;
        font-size: 14px;
        color: #fff;
        border: none;
        cursor: pointer;
        &:active {
          opacity: 0.5;
        }
      }
    }
  }
}
</style>

<style lang="less">
.station-modal {
  .ant-modal-footer {
    display: none !important;
  }
  .ant-modal-body {
    height: 266px !important;
    padding-top: 16px !important;
  }
}
</style>
